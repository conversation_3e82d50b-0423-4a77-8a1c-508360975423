#!/usr/bin/env node

/**
 * Demo script to showcase the user management API functionality
 * Run with: node demo.js
 */

const API_BASE = 'http://localhost:3001';

async function makeRequest(method, endpoint, data = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 0, error: error.message };
  }
}

async function demo() {
  console.log('🚀 User Management API Demo\n');

  // 1. Health Check
  console.log('1. Health Check');
  const health = await makeRequest('GET', '/health');
  console.log(`Status: ${health.status}`);
  console.log(`Response:`, health.data);
  console.log('');

  // 2. Register a new user
  console.log('2. Register New User');
  const newUser = {
    email: '<EMAIL>',
    password: 'demopassword123',
    name: 'Demo User',
    image: 'https://example.com/avatar.jpg'
  };
  const register = await makeRequest('POST', '/api/auth/sign-up/email', newUser);
  console.log(`Status: ${register.status}`);
  console.log(`Response:`, register.data);
  console.log('');

  // 3. Login
  console.log('3. User Login');
  const login = await makeRequest('POST', '/api/auth/sign-in/email', {
    email: newUser.email,
    password: newUser.password
  });
  console.log(`Status: ${login.status}`);
  console.log(`Response:`, login.data);
  console.log('');

  // 4. Get all users (admin endpoint)
  console.log('4. Get All Users');
  const users = await makeRequest('GET', '/api/users?page=1&limit=5');
  console.log(`Status: ${users.status}`);
  console.log(`Response:`, users.data);
  console.log('');

  // 5. Password Reset Request
  console.log('5. Password Reset Request');
  const resetRequest = await makeRequest('POST', '/api/auth/forget-password', {
    email: newUser.email
  });
  console.log(`Status: ${resetRequest.status}`);
  console.log(`Response:`, resetRequest.data);
  console.log('');

  // 6. Get session
  console.log('6. Get Current Session');
  const session = await makeRequest('GET', '/api/auth/session');
  console.log(`Status: ${session.status}`);
  console.log(`Response:`, session.data);
  console.log('');

  console.log('✅ Demo completed!');
  console.log('\n📚 API Documentation available at: http://localhost:3001/documentation');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/health`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running!');
    console.log('Please start the server first:');
    console.log('  pnpm dev');
    console.log('');
    process.exit(1);
  }

  await demo();
}

// Import fetch for Node.js if needed
if (typeof fetch === 'undefined') {
  console.log('Installing node-fetch for demo...');
  try {
    const { default: fetch } = await import('node-fetch');
    global.fetch = fetch;
  } catch (error) {
    console.log('Please install node-fetch: npm install node-fetch');
    process.exit(1);
  }
}

main().catch(console.error);
