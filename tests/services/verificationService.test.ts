import { describe, it, expect, beforeEach, vi } from 'vitest';
import { VerificationService } from '../../src/services/verificationService';
import { testPrisma, databaseAvailable } from '../setup';

// Mock FastifyInstance
const createMockApp = () => ({
  config: {
    NODE_ENV: 'test',
  },
  log: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
} as any);

describe('VerificationService', () => {
  let verificationService: VerificationService;
  let mockApp: any;

  beforeEach(async () => {
    if (!databaseAvailable) return;
    
    // Clean up before each test
    await testPrisma.verificationCode.deleteMany();
    mockApp = createMockApp();
    verificationService = new VerificationService(mockApp);
    vi.clearAllMocks();
  });

  describe('sendVerificationCode', () => {
    it('should send verification code successfully', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Mock email service to avoid actual email sending
      vi.spyOn(verificationService['emailService'], 'sendVerificationCode').mockResolvedValue();

      await verificationService.sendVerificationCode(email, type);

      // Check if code was stored in database
      const storedCode = await testPrisma.verificationCode.findFirst({
        where: { email, type },
      });

      expect(storedCode).toBeDefined();
      expect(storedCode?.email).toBe(email);
      expect(storedCode?.type).toBe(type);
      expect(storedCode?.used).toBe(false);
      expect(storedCode?.code).toHaveLength(6);
    });

    it('should handle email sending failure', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Mock email service to throw error
      vi.spyOn(verificationService['emailService'], 'sendVerificationCode').mockRejectedValue(new Error('Email failed'));

      await expect(verificationService.sendVerificationCode(email, type)).rejects.toThrow('Failed to send verification code');
    });
  });

  describe('verifyCode', () => {
    it('should verify valid code successfully', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const code = '123456';
      const type = 'registration';

      // Create verification code in database
      await testPrisma.verificationCode.create({
        data: {
          email,
          code,
          type,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
          used: false,
        },
      });

      const result = await verificationService.verifyCode(email, code, type);

      expect(result).toBe(true);

      // Check if code was marked as used
      const updatedCode = await testPrisma.verificationCode.findFirst({
        where: { email, code, type },
      });
      expect(updatedCode?.used).toBe(true);
    });

    it('should reject invalid code', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const code = '123456';
      const type = 'registration';

      const result = await verificationService.verifyCode(email, code, type);

      expect(result).toBe(false);
    });

    it('should reject expired code', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const code = '123456';
      const type = 'registration';

      // Create expired verification code
      await testPrisma.verificationCode.create({
        data: {
          email,
          code,
          type,
          expiresAt: new Date(Date.now() - 1000), // 1 second ago (expired)
          used: false,
        },
      });

      const result = await verificationService.verifyCode(email, code, type);

      expect(result).toBe(false);
    });

    it('should reject already used code', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const code = '123456';
      const type = 'registration';

      // Create used verification code
      await testPrisma.verificationCode.create({
        data: {
          email,
          code,
          type,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: true, // Already used
        },
      });

      const result = await verificationService.verifyCode(email, code, type);

      expect(result).toBe(false);
    });
  });

  describe('canRequestNewCode', () => {
    it('should allow new code request when no recent code exists', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      const canRequest = await verificationService.canRequestNewCode(email, type);

      expect(canRequest).toBe(true);
    });

    it('should reject new code request when recent code exists', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Create recent verification code (within last minute)
      await testPrisma.verificationCode.create({
        data: {
          email,
          code: '123456',
          type,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false,
          createdAt: new Date(), // Just created
        },
      });

      const canRequest = await verificationService.canRequestNewCode(email, type);

      expect(canRequest).toBe(false);
    });
  });

  describe('hasExceededDailyLimit', () => {
    it('should return false when under daily limit', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Create 3 codes (under limit of 5)
      for (let i = 0; i < 3; i++) {
        await testPrisma.verificationCode.create({
          data: {
            email,
            code: `12345${i}`,
            type,
            expiresAt: new Date(Date.now() + 10 * 60 * 1000),
            used: false,
          },
        });
      }

      const hasExceeded = await verificationService.hasExceededDailyLimit(email, type);

      expect(hasExceeded).toBe(false);
    });

    it('should return true when daily limit exceeded', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Create 5 codes (at limit)
      for (let i = 0; i < 5; i++) {
        await testPrisma.verificationCode.create({
          data: {
            email,
            code: `12345${i}`,
            type,
            expiresAt: new Date(Date.now() + 10 * 60 * 1000),
            used: false,
          },
        });
      }

      const hasExceeded = await verificationService.hasExceededDailyLimit(email, type);

      expect(hasExceeded).toBe(true);
    });
  });

  describe('cleanupExpiredCodes', () => {
    it('should clean up expired and used codes', async () => {
      if (!databaseAvailable) return;

      const email = '<EMAIL>';
      const type = 'registration';

      // Create expired code
      await testPrisma.verificationCode.create({
        data: {
          email,
          code: '111111',
          type,
          expiresAt: new Date(Date.now() - 1000), // Expired
          used: false,
        },
      });

      // Create used code
      await testPrisma.verificationCode.create({
        data: {
          email,
          code: '222222',
          type,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: true, // Used
        },
      });

      // Create valid code
      await testPrisma.verificationCode.create({
        data: {
          email,
          code: '333333',
          type,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false, // Valid
        },
      });

      await verificationService.cleanupExpiredCodes();

      // Check remaining codes
      const remainingCodes = await testPrisma.verificationCode.findMany();
      expect(remainingCodes).toHaveLength(1);
      expect(remainingCodes[0].code).toBe('333333');
    });
  });
});
