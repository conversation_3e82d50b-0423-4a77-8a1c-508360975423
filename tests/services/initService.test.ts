import { describe, it, expect, beforeEach, vi } from 'vitest';
import { InitService } from '../../src/services/initService';
import { UserService } from '../../src/services/userService';
import { testPrisma } from '../setup';

// Mock FastifyInstance
const createMockApp = () => ({
  config: {
    ADMIN_EMAIL: '<EMAIL>',
    ADMIN_PASSWORD: 'adminpassword123',
    TEST_EMAIL: '<EMAIL>',
    TEST_PASSWORD: 'testpassword123',
  },
  log: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
} as any);

describe('InitService', () => {
  let mockApp: any;

  beforeEach(async () => {
    // Clean up before each test
    await testPrisma.user.deleteMany();
    mockApp = createMockApp();
    vi.clearAllMocks();
  });

  describe('initializeDefaultAccounts', () => {
    it('should create admin and test accounts when they do not exist', async () => {
      await InitService.initializeDefaultAccounts(mockApp);

      // Check admin account
      const admin = await UserService.findByEmail(mockApp.config.ADMIN_EMAIL);
      expect(admin).toBeDefined();
      expect(admin?.role).toBe('admin');
      expect(admin?.name).toBe('Administrator');

      // Check test account
      const testUser = await UserService.findByEmail(mockApp.config.TEST_EMAIL);
      expect(testUser).toBeDefined();
      expect(testUser?.role).toBe('user');
      expect(testUser?.name).toBe('Test User');

      // Check log messages
      expect(mockApp.log.info).toHaveBeenCalledWith('🔧 Initializing default accounts...');
      expect(mockApp.log.info).toHaveBeenCalledWith('✅ Default accounts initialization completed');
    });

    it('should skip creating admin account if it already exists', async () => {
      // Create admin account first
      await UserService.createUser({
        email: mockApp.config.ADMIN_EMAIL,
        password: 'existingpassword',
        name: 'Existing Admin',
        role: 'user', // Different role to test update
      });

      await InitService.initializeDefaultAccounts(mockApp);

      // Check that admin role was updated
      const admin = await UserService.findByEmail(mockApp.config.ADMIN_EMAIL);
      expect(admin?.role).toBe('admin');

      // Check log message
      expect(mockApp.log.info).toHaveBeenCalledWith(
        `👤 Admin account already exists: ${mockApp.config.ADMIN_EMAIL}`
      );
      expect(mockApp.log.info).toHaveBeenCalledWith(
        `🔄 Updated existing user ${mockApp.config.ADMIN_EMAIL} to admin role`
      );
    });

    it('should skip creating test account if it already exists', async () => {
      // Create test account first
      await UserService.createUser({
        email: mockApp.config.TEST_EMAIL,
        password: 'existingpassword',
        name: 'Existing Test User',
        role: 'user',
      });

      await InitService.initializeDefaultAccounts(mockApp);

      // Check that test user still exists and wasn't recreated
      const testUser = await UserService.findByEmail(mockApp.config.TEST_EMAIL);
      expect(testUser?.name).toBe('Existing Test User'); // Should keep original name

      // Check log message
      expect(mockApp.log.info).toHaveBeenCalledWith(
        `👤 Test account already exists: ${mockApp.config.TEST_EMAIL}`
      );
    });

    it('should skip test account creation when credentials are not configured', async () => {
      const appWithoutTest = {
        ...mockApp,
        config: {
          ...mockApp.config,
          TEST_EMAIL: '',
          TEST_PASSWORD: '',
        },
      };

      await InitService.initializeDefaultAccounts(appWithoutTest);

      // Check that only admin was created
      const admin = await UserService.findByEmail(mockApp.config.ADMIN_EMAIL);
      expect(admin).toBeDefined();

      const testUser = await UserService.findByEmail(mockApp.config.TEST_EMAIL);
      expect(testUser).toBeNull();
    });

    it('should handle errors during initialization', async () => {
      // Create a user with the admin email first to cause a conflict
      await UserService.createUser({
        email: mockApp.config.ADMIN_EMAIL,
        password: 'existingpassword',
        name: 'Existing User',
        role: 'user',
      });

      // Mock UserService.createUser to throw an error on the second call
      const originalCreateUser = UserService.createUser;
      vi.spyOn(UserService, 'createUser').mockImplementationOnce(() => {
        throw new Error('Database error');
      });

      await expect(InitService.initializeDefaultAccounts(mockApp)).rejects.toThrow();
      expect(mockApp.log.error).toHaveBeenCalled();

      // Restore original method
      UserService.createUser = originalCreateUser;
    });
  });

  describe('validateConfiguration', () => {
    it('should pass validation with valid configuration', () => {
      expect(() => InitService.validateConfiguration(mockApp)).not.toThrow();
    });

    it('should throw error for missing admin email', () => {
      const appWithoutAdminEmail = {
        ...mockApp,
        config: {
          ...mockApp.config,
          ADMIN_EMAIL: '',
        },
      };

      expect(() => InitService.validateConfiguration(appWithoutAdminEmail)).toThrow(
        'Missing required environment variables: ADMIN_EMAIL'
      );
    });

    it('should throw error for missing admin password', () => {
      const appWithoutAdminPassword = {
        ...mockApp,
        config: {
          ...mockApp.config,
          ADMIN_PASSWORD: '',
        },
      };

      expect(() => InitService.validateConfiguration(appWithoutAdminPassword)).toThrow(
        'Missing required environment variables: ADMIN_PASSWORD'
      );
    });

    it('should throw error for invalid admin email format', () => {
      const appWithInvalidEmail = {
        ...mockApp,
        config: {
          ...mockApp.config,
          ADMIN_EMAIL: 'invalid-email',
        },
      };

      expect(() => InitService.validateConfiguration(appWithInvalidEmail)).toThrow(
        'ADMIN_EMAIL must be a valid email address'
      );
    });

    it('should throw error for invalid test email format', () => {
      const appWithInvalidTestEmail = {
        ...mockApp,
        config: {
          ...mockApp.config,
          TEST_EMAIL: 'invalid-email',
        },
      };

      expect(() => InitService.validateConfiguration(appWithInvalidTestEmail)).toThrow(
        'TEST_EMAIL must be a valid email address'
      );
    });

    it('should throw error for short admin password', () => {
      const appWithShortPassword = {
        ...mockApp,
        config: {
          ...mockApp.config,
          ADMIN_PASSWORD: '123',
        },
      };

      expect(() => InitService.validateConfiguration(appWithShortPassword)).toThrow(
        'ADMIN_PASSWORD must be at least 8 characters long'
      );
    });

    it('should throw error for short test password', () => {
      const appWithShortTestPassword = {
        ...mockApp,
        config: {
          ...mockApp.config,
          TEST_PASSWORD: '123',
        },
      };

      expect(() => InitService.validateConfiguration(appWithShortTestPassword)).toThrow(
        'TEST_PASSWORD must be at least 8 characters long'
      );
    });

    it('should pass validation when test credentials are not provided', () => {
      const appWithoutTestCredentials = {
        ...mockApp,
        config: {
          ...mockApp.config,
          TEST_EMAIL: '',
          TEST_PASSWORD: '',
        },
      };

      expect(() => InitService.validateConfiguration(appWithoutTestCredentials)).not.toThrow();
    });
  });
});
