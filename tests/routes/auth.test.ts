import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';
import { testPrisma, databaseAvailable } from '../setup';

describe('Auth Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    if (!databaseAvailable) return;
    
    // Clean up database first
    await testPrisma.verificationCode.deleteMany();
    await testPrisma.user.deleteMany();
    
    // Set unique test environment variables for each test
    process.env.ADMIN_EMAIL = `admin-${Date.now()}@example.com`;
    process.env.ADMIN_PASSWORD = 'testpassword123';
    
    // Create a fresh app instance for each test
    app = await createApp();
    await app.ready();
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('POST /api/auth/send-code', () => {
    it('should send verification code for registration', async () => {
      if (!databaseAvailable) return;

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/send-code',
        payload: {
          email: '<EMAIL>',
          type: 'registration',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('Verification code sent successfully');
      expect(responseBody.email).toBe('<EMAIL>');

      // Check if code was stored in database
      const storedCode = await testPrisma.verificationCode.findFirst({
        where: {
          email: '<EMAIL>',
          type: 'registration',
        },
      });
      expect(storedCode).toBeDefined();
    });

    it('should reject registration code for existing user', async () => {
      if (!databaseAvailable) return;

      // Create existing user first
      await testPrisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          name: 'Existing User',
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/send-code',
        payload: {
          email: '<EMAIL>',
          type: 'registration',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('User Exists');
    });

    it('should send password reset code for existing user', async () => {
      if (!databaseAvailable) return;

      // Create existing user first
      await testPrisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          name: 'Existing User',
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/send-code',
        payload: {
          email: '<EMAIL>',
          type: 'password-reset',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('Verification code sent successfully');
    });

    it('should reject password reset code for non-existing user', async () => {
      if (!databaseAvailable) return;

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/send-code',
        payload: {
          email: '<EMAIL>',
          type: 'password-reset',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('User Not Found');
    });

    it('should reject invalid email format', async () => {
      if (!databaseAvailable) return;

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/send-code',
        payload: {
          email: 'invalid-email',
          type: 'registration',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Invalid Email');
    });
  });

  describe('POST /api/auth/verify-code', () => {
    it('should verify valid code', async () => {
      if (!databaseAvailable) return;

      // Create verification code in database
      await testPrisma.verificationCode.create({
        data: {
          email: '<EMAIL>',
          code: '123456',
          type: 'registration',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
          used: false,
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/verify-code',
        payload: {
          email: '<EMAIL>',
          code: '123456',
          type: 'registration',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.valid).toBe(true);
      expect(responseBody.message).toBe('Code verified successfully');
    });

    it('should reject invalid code', async () => {
      if (!databaseAvailable) return;

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/verify-code',
        payload: {
          email: '<EMAIL>',
          code: '999999',
          type: 'registration',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Invalid Code');
    });
  });

  describe('POST /api/auth/register-with-code', () => {
    it('should register user with valid code', async () => {
      if (!databaseAvailable) return;

      // Create verification code in database
      await testPrisma.verificationCode.create({
        data: {
          email: '<EMAIL>',
          code: '123456',
          type: 'registration',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false,
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register-with-code',
        payload: {
          email: '<EMAIL>',
          code: '123456',
          password: 'password123',
          name: 'New User',
          avatar: 'https://example.com/avatar.jpg',
        },
      });

      expect(response.statusCode).toBe(201);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('User registered successfully');
      expect(responseBody.user.email).toBe('<EMAIL>');
      expect(responseBody.user.name).toBe('New User');

      // Check if user was created in database
      const createdUser = await testPrisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      expect(createdUser).toBeDefined();
      expect(createdUser?.name).toBe('New User');
    });

    it('should reject registration with invalid code', async () => {
      if (!databaseAvailable) return;

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register-with-code',
        payload: {
          email: '<EMAIL>',
          code: '999999',
          password: 'password123',
          name: 'New User',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Invalid Code');
    });

    it('should reject registration for existing user', async () => {
      if (!databaseAvailable) return;

      // Create existing user
      await testPrisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          name: 'Existing User',
        },
      });

      // Create verification code
      await testPrisma.verificationCode.create({
        data: {
          email: '<EMAIL>',
          code: '123456',
          type: 'registration',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false,
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/register-with-code',
        payload: {
          email: '<EMAIL>',
          code: '123456',
          password: 'password123',
          name: 'New User',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('User Exists');
    });
  });

  describe('POST /api/auth/reset-password-with-code', () => {
    it('should reset password with valid code', async () => {
      if (!databaseAvailable) return;

      // Create existing user
      const user = await testPrisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'oldhashedpassword',
          name: 'Test User',
        },
      });

      // Create verification code
      await testPrisma.verificationCode.create({
        data: {
          email: '<EMAIL>',
          code: '123456',
          type: 'password-reset',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false,
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/reset-password-with-code',
        payload: {
          email: '<EMAIL>',
          code: '123456',
          newPassword: 'newpassword123',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.message).toBe('Password reset successfully');

      // Check if password was updated (should be different from original)
      const updatedUser = await testPrisma.user.findUnique({
        where: { id: user.id },
      });
      expect(updatedUser?.password).not.toBe('oldhashedpassword');
    });

    it('should reject password reset with invalid code', async () => {
      if (!databaseAvailable) return;

      // Create existing user
      await testPrisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          name: 'Test User',
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/reset-password-with-code',
        payload: {
          email: '<EMAIL>',
          code: '999999',
          newPassword: 'newpassword123',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('Invalid Code');
    });

    it('should reject password reset for non-existing user', async () => {
      if (!databaseAvailable) return;

      // Create verification code for non-existing user
      await testPrisma.verificationCode.create({
        data: {
          email: '<EMAIL>',
          code: '123456',
          type: 'password-reset',
          expiresAt: new Date(Date.now() + 10 * 60 * 1000),
          used: false,
        },
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/reset-password-with-code',
        payload: {
          email: '<EMAIL>',
          code: '123456',
          newPassword: 'newpassword123',
        },
      });

      expect(response.statusCode).toBe(400);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.error).toBe('User Not Found');
    });
  });
});
