# Test Environment Configuration
NODE_ENV=test
PORT=3002

# Test Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/rsdh_test"

# CORS
CORS_ORIGIN="http://localhost:3000"
TRUSTED_ORIGINS="http://localhost:3000"

# Auth
AUTH_URL="http://localhost:3002"
JWT_SECRET="test-jwt-secret-key-for-testing-only"
JWT_EXPIRES_IN="1d"
REFRESH_TOKEN_EXPIRES_IN="7d"

# Test Admin Account
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="testadmin123"

# Test User Account
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="testuser123"
