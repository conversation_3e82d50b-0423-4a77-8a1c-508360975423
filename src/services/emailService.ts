import nodemailer from 'nodemailer';
import { FastifyInstance } from 'fastify';

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface VerificationCodeOptions {
  email: string;
  code: string;
  type: 'registration' | 'password-reset' | 'email-verification';
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private app: FastifyInstance;

  constructor(app: FastifyInstance) {
    this.app = app;
    this.transporter = this.createTransporter();
  }

  /**
   * Create email transporter based on environment
   */
  private createTransporter(): nodemailer.Transporter {
    // Use environment variables from .env file
    const emailHost = this.app.config.EMAIL_HOST;
    const emailPort = this.app.config.EMAIL_PORT;
    const emailUser = this.app.config.EMAIL_USER;
    const emailPass = this.app.config.EMAIL_PASS;

    // In development, log email configuration
    if (this.app.config.NODE_ENV === 'development') {
      this.app.log.info(`📧 Email configuration: ${emailHost}:${emailPort} (${emailUser})`);
    }

    // Use real SMTP settings from environment
    return nodemailer.createTransport({
      host: emailHost,
      port: parseInt(emailPort?.toString() || '587'),
      secure: emailPort === 465, // Use SSL for port 465
      auth: {
        user: emailUser,
        pass: emailPass,
      },
    });
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      // In development, just log the email instead of sending
      if (this.app.config.NODE_ENV === 'development') {
        this.app.log.info(`📧 Email would be sent to: ${options.to}`);
        this.app.log.info(`📧 Subject: ${options.subject}`);
        this.app.log.info(`📧 Content: ${options.text || 'HTML content'}`);
        return;
      }

      const info = await this.transporter.sendMail({
        from: this.app.config.EMAIL_FROM || `"${this.app.config.APP_NAME}" <${this.app.config.EMAIL_USER}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });

      this.app.log.info(`Email sent: ${info.messageId}`);
    } catch (error) {
      this.app.log.error('Failed to send email:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send verification code email
   */
  async sendVerificationCode(options: VerificationCodeOptions): Promise<void> {
    const { email, code, type } = options;

    let subject: string;
    let html: string;
    let text: string;

    switch (type) {
      case 'registration':
        subject = '欢迎注册 - 验证您的邮箱';
        html = this.getRegistrationEmailTemplate(code);
        text = `欢迎注册！您的验证码是：${code}。此验证码将在10分钟后过期。`;
        break;

      case 'password-reset':
        subject = '重置密码 - 验证码';
        html = this.getPasswordResetEmailTemplate(code);
        text = `您请求重置密码。您的验证码是：${code}。此验证码将在10分钟后过期。如果您没有请求重置密码，请忽略此邮件。`;
        break;

      case 'email-verification':
        subject = '邮箱验证 - 验证码';
        html = this.getEmailVerificationTemplate(code);
        text = `请验证您的邮箱地址。您的验证码是：${code}。此验证码将在10分钟后过期。`;
        break;

      default:
        throw new Error('Invalid verification code type');
    }

    await this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }

  /**
   * Generate registration email template
   */
  private getRegistrationEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎注册</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #007bff; text-align: center; padding: 20px; background: white; border: 2px dashed #007bff; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎注册人生导航</h1>
          </div>
          <div class="content">
            <p>感谢您注册我们的服务！</p>
            <p>请使用以下验证码完成注册：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
            <p>如果您没有注册账户，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate password reset email template
   */
  private getPasswordResetEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>重置密码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background: white; border: 2px dashed #dc3545; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>重置密码</h1>
          </div>
          <div class="content">
            <p>您请求重置密码。</p>
            <p>请使用以下验证码重置您的密码：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
            <div class="warning">
              <strong>安全提醒：</strong>如果您没有请求重置密码，请立即忽略此邮件并检查您的账户安全。
            </div>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email verification template
   */
  private getEmailVerificationTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .code { font-size: 24px; font-weight: bold; color: #28a745; text-align: center; padding: 20px; background: white; border: 2px dashed #28a745; margin: 20px 0; }
          .footer { text-align: center; color: #666; font-size: 12px; padding: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>验证邮箱地址</h1>
          </div>
          <div class="content">
            <p>请验证您的邮箱地址以完成设置。</p>
            <p>请使用以下验证码：</p>
            <div class="code">${code}</div>
            <p>此验证码将在 <strong>10分钟</strong> 后过期。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate random verification code
   */
  static generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
