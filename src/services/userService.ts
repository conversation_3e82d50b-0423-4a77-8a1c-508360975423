import { prisma } from '../lib/prisma';
import bcrypt from 'bcrypt';
import { CreateUserInput, UpdateUserInput, UserResponse } from '../types/user';

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  /**
   * Hash a password
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Verify a password
   */
  static async verifyPassword(password: string, hashedPassword: string | null): Promise<boolean> {
    if (!hashedPassword) return false;
    return bcrypt.compare(password, hashedPassword);
  }

  /**
   * Create a new user
   */
  static async createUser(input: CreateUserInput): Promise<UserResponse> {
    const hashedPassword = await this.hashPassword(input.password);

    const user = await prisma.user.create({
      data: {
        email: input.email,
        password: hashedPassword,
        name: input.name,
        avatar: input.avatar,
        role: input.role || 'user',
      },
    });

    return this.toUserResponse(user);
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<UserResponse | null> {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    return user ? this.toUserResponse(user) : null;
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<UserResponse | null> {
    const user = await prisma.user.findUnique({
      where: { id },
    });

    return user ? this.toUserResponse(user) : null;
  }

  /**
   * Get user by ID (alias for findById)
   */
  static async getUserById(id: string): Promise<UserResponse | null> {
    return this.findById(id);
  }

  /**
   * Update user
   */
  static async updateUser(id: string, input: UpdateUserInput): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: input,
    });

    return this.toUserResponse(user);
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string): Promise<void> {
    await prisma.user.delete({
      where: { id },
    });
  }

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(page = 1, limit = 10): Promise<{ users: UserResponse[]; total: number }> {
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count(),
    ]);

    return {
      users: users.map(this.toUserResponse),
      total,
    };
  }

  /**
   * Check if user exists
   */
  static async userExists(email: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true },
    });
    return !!user;
  }

  /**
   * Convert database user to response format
   */
  private static toUserResponse(user: any): UserResponse {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      emailVerified: user.emailVerified,
      disabled: user.disabled,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  /**
   * Enable/disable user
   */
  static async toggleUserStatus(id: string, disabled: boolean): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: { disabled },
    });

    return this.toUserResponse(user);
  }

  /**
   * Update user role
   */
  static async updateUserRole(id: string, role: string): Promise<UserResponse> {
    const user = await prisma.user.update({
      where: { id },
      data: { role },
    });

    return this.toUserResponse(user);
  }

  /**
   * Update user password
   */
  static async updatePassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await this.hashPassword(newPassword);
    await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });
  }
}
