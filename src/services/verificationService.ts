import { prisma } from '../lib/prisma';
import { EmailService } from './emailService';
import { FastifyInstance } from 'fastify';

export interface VerificationCode {
  id: string;
  email: string;
  code: string;
  type: 'registration' | 'password-reset' | 'email-verification';
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

export class VerificationService {
  private emailService: EmailService;
  private app: FastifyInstance;

  constructor(app: FastifyInstance) {
    this.app = app;
    this.emailService = new EmailService(app);
  }

  /**
   * Send verification code
   */
  async sendVerificationCode(
    email: string, 
    type: 'registration' | 'password-reset' | 'email-verification'
  ): Promise<void> {
    // Generate verification code
    const code = EmailService.generateVerificationCode();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    try {
      // Store verification code in database
      await prisma.verificationCode.create({
        data: {
          email,
          code,
          type,
          expiresAt,
          used: false,
        },
      });

      // Send email
      await this.emailService.sendVerificationCode({
        email,
        code,
        type,
      });

      this.app.log.info(`Verification code sent to ${email} for ${type}`);
    } catch (error) {
      this.app.log.error('Failed to send verification code:', error);
      throw new Error('Failed to send verification code');
    }
  }

  /**
   * Verify code
   */
  async verifyCode(
    email: string, 
    code: string, 
    type: 'registration' | 'password-reset' | 'email-verification'
  ): Promise<boolean> {
    try {
      // Find valid verification code
      const verificationCode = await prisma.verificationCode.findFirst({
        where: {
          email,
          code,
          type,
          used: false,
          expiresAt: {
            gt: new Date(),
          },
        },
      });

      if (!verificationCode) {
        return false;
      }

      // Mark as used
      await prisma.verificationCode.update({
        where: { id: verificationCode.id },
        data: { used: true },
      });

      this.app.log.info(`Verification code verified for ${email} (${type})`);
      return true;
    } catch (error) {
      this.app.log.error('Failed to verify code:', error);
      return false;
    }
  }

  /**
   * Clean up expired verification codes
   */
  async cleanupExpiredCodes(): Promise<void> {
    try {
      const result = await prisma.verificationCode.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { used: true },
          ],
        },
      });

      this.app.log.info(`Cleaned up ${result.count} expired verification codes`);
    } catch (error) {
      this.app.log.error('Failed to cleanup expired codes:', error);
    }
  }

  /**
   * Check if user can request new code (rate limiting)
   */
  async canRequestNewCode(email: string, type: string): Promise<boolean> {
    try {
      // Check if there's a recent code (within last minute)
      const recentCode = await prisma.verificationCode.findFirst({
        where: {
          email,
          type,
          createdAt: {
            gt: new Date(Date.now() - 60 * 1000), // 1 minute ago
          },
        },
      });

      return !recentCode;
    } catch (error) {
      this.app.log.error('Failed to check rate limit:', error);
      return false;
    }
  }

  /**
   * Get verification code attempts count
   */
  async getAttemptCount(email: string, type: string): Promise<number> {
    try {
      const count = await prisma.verificationCode.count({
        where: {
          email,
          type,
          createdAt: {
            gt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      });

      return count;
    } catch (error) {
      this.app.log.error('Failed to get attempt count:', error);
      return 0;
    }
  }

  /**
   * Check if user has exceeded daily limit
   */
  async hasExceededDailyLimit(email: string, type: string): Promise<boolean> {
    const attemptCount = await this.getAttemptCount(email, type);
    const dailyLimit = 5; // Maximum 5 attempts per day
    return attemptCount >= dailyLimit;
  }
}
