import { createApp } from './app';
import { prisma } from './lib/prisma';

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Consider whether you want to exit the process here
  // process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Consider whether you want to exit the process here
  // process.exit(1);
});

import { FastifyInstance } from 'fastify';

async function start() {
  let app: FastifyInstance;

  try {
    // Create Fastify app
    app = await createApp();

    // Test database connection
    await prisma.$connect();
    app.log.info('✅ Database connected successfully');

    // Function to try starting the server on a given port
    const startServer = async (port: number, maxAttempts = 3, attempt = 1): Promise<string> => {
      try {
        const address = await app.listen({
          port,
          host: '0.0.0.0'
        });
        return address;
      } catch (error: unknown) {
        if (error instanceof Error && 'code' in error && error.code === 'EADDRINUSE' && attempt < maxAttempts) {
          app.log.warn(`Port ${port} is in use, trying port ${port + 1}...`);
          return startServer(port + 1, maxAttempts, attempt + 1);
        }
        throw error;
      }
    };

    // Start the server with port fallback
    const address = await startServer(app.config.PORT);

    app.log.info(`🚀 Server started in ${app.config.NODE_ENV} mode`);
    app.log.info(`🌐 Server running at ${address}`);
    app.log.info(`📊 Health check at ${address}/health`);

  } catch (err) {
    console.error('❌ Error starting server:', err);
    process.exit(1);
  }

  // Graceful shutdown
  const shutdown = async (signal: string) => {
    app.log.info(`\n${signal} received. Shutting down gracefully...`);

    try {
      // Close the Fastify app
      if (app) {
        await app.close();
      }

      // Disconnect Prisma
      await prisma.$disconnect();

      app.log.info('✅ Server stopped successfully');
      process.exit(0);

    } catch (err) {
      console.error('❌ Error during shutdown:', err);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  // Handle uncaught exceptions and unhandled rejections in production
  if (process.env.NODE_ENV === 'production') {
    process.on('uncaughtException', (err) => {
      console.error('Uncaught Exception:', err);
      shutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('UNHANDLED_REJECTION');
    });
  }
}

// Start the application
start().catch((err) => {
  console.error('Fatal error during application startup:', err);
  process.exit(1);
});