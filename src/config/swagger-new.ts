import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifySwagger from '@fastify/swagger';
import fastifySwagger<PERSON> from '@fastify/swagger-ui';

declare module 'fastify' {
  interface FastifySchema {
    hide?: boolean;
  }
}

export async function registerSwagger(app: FastifyInstance): Promise<void> {
  // Note: better-auth doesn't provide generateOpenAPISchema method
  // We'll let Fastify auto-generate the documentation from route schemas

  try {
    // Register Swagger
    await app.register(fastifySwagger, {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'Life Navigation API',
          description: 'API documentation for Life Navigation application with user management',
          version: '1.0.0',
        },
        servers: [
          { url: 'http://localhost:3001', description: 'Development' },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        paths: {
          // Paths will be auto-generated from route schemas
        },
        tags: [
          { name: 'Authentication', description: 'Authentication endpoints' },
          { name: 'Users', description: 'User management endpoints' },
          { name: 'Health', description: 'Health check endpoints' }
        ]
      },
      hideUntagged: false // Show all routes, even those without tags
    });

    // Register Swagger UI
    await app.register(fastifySwaggerUI, {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
      transformSpecification: (swaggerObject: Record<string, unknown>) => {
        // Ensure the base path is correct
        swaggerObject.servers = [{ url: '/' }];
        return swaggerObject;
      },
    });

    // Add a redirect from /docs to /documentation
    app.get('/docs', async (_request: FastifyRequest, reply: FastifyReply) => {
      return reply.redirect('/documentation/');
    });

    app.log.info('Swagger documentation is available at /documentation');
  } catch (error) {
    app.log.error('Failed to initialize Swagger:', error);
    throw error;
  }
}
