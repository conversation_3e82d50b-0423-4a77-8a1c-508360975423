import { FastifyPluginAsync } from 'fastify';
import fp from 'fastify-plugin';
import fastifyEnv from '@fastify/env';
import { isEmail } from 'validator';

const validateEmail = (email: string, fieldName: string) => {
  if (email && !isEmail(email)) {
    throw new Error(`${fieldName} must be a valid email address`);
  }
  return true;
};

declare module 'fastify' {
  interface FastifyInstance {
    config: {
      DATABASE_URL: string;
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: number;
      APP_NAME: string;
      APP_URL: string;
      CORS_ORIGIN: string;
      TRUSTED_ORIGINS: string;
      AUTH_URL: string;
      JWT_SECRET: string;
      JWT_EXPIRES_IN: string;
      REFRESH_TOKEN_EXPIRES_IN: string;
      ADMIN_EMAIL: string;
      ADMIN_PASSWORD: string;
      TEST_EMAIL: string;
      TEST_PASSWORD: string;
      SUPPORT_EMAIL: string;
      EMAIL_HOST: string;
      EMAIL_PORT: number;
      EMAIL_USER: string;
      EMAIL_PASS: string;
      EMAIL_FROM: string;
    };
  }
}

const schema = {
  type: 'object',
  required: [ 'DATABASE_URL', 'JWT_SECRET', 'ADMIN_EMAIL', 'ADMIN_PASSWORD', 'EMAIL_HOST', 'EMAIL_USER', 'EMAIL_PASS' ],
  properties: {
    DATABASE_URL: { type: 'string', default: 'postgresql://postgres:postgres@localhost:5432/rsdh_dev' },
    NODE_ENV: { type: 'string', default: 'development' },
    PORT: { type: 'number', default: 3001 },
    APP_NAME: { type: 'string', default: 'Life Navigation' },
    APP_URL: { type: 'string', default: 'http://localhost:3000' },
    CORS_ORIGIN: { type: 'string', default: 'http://localhost:3000' },
    TRUSTED_ORIGINS: { type: 'string', default: 'http://localhost:3000' },
    AUTH_URL: { type: 'string', default: 'http://localhost:3000' },
    JWT_SECRET: { type: 'string', default: 'your-secret-key-here' },
    JWT_EXPIRES_IN: { type: 'string', default: '1d' },
    REFRESH_TOKEN_EXPIRES_IN: { type: 'string', default: '7d' },
    ADMIN_EMAIL: { type: 'string', default: '<EMAIL>' },
    ADMIN_PASSWORD: { type: 'string' },
    TEST_EMAIL: { type: 'string', default: '<EMAIL>' },
    TEST_PASSWORD: { type: 'string' },
    SUPPORT_EMAIL: { type: 'string', default: '<EMAIL>' },
    EMAIL_HOST: { type: 'string' },
    EMAIL_PORT: { type: 'number', default: 587 },
    EMAIL_USER: { type: 'string' },
    EMAIL_PASS: { type: 'string' },
    EMAIL_FROM: { type: 'string' },
  }
};

const envPlugin: FastifyPluginAsync = fp(async (fastify) => {
  await fastify.register(fastifyEnv, {
    confKey: 'config',
    schema: schema,
    dotenv: true
  });

  // Post-validation logic
  validateEmail(fastify.config.ADMIN_EMAIL, 'ADMIN_EMAIL');
  if (fastify.config.TEST_EMAIL) {
    validateEmail(fastify.config.TEST_EMAIL, 'TEST_EMAIL');
  }

  if (fastify.config.ADMIN_PASSWORD && fastify.config.ADMIN_PASSWORD.length < 8) {
    throw new Error('ADMIN_PASSWORD must be at least 8 characters long');
  }
});

export default envPlugin;

