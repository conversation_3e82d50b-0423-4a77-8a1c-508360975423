#!/usr/bin/env node

const http = require('http');

const BASE_URL = 'http://127.0.0.1:3002';

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ statusCode: res.statusCode, body: jsonBody, headers: res.headers });
        } catch (e) {
          resolve({ statusCode: res.statusCode, body: body, headers: res.headers });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Starting API Tests...\n');

  let adminToken = '';
  let userToken = '';
  let userId = '';

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await makeRequest('GET', '/health');
    console.log(`   Status: ${healthResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(healthResponse.body)}`);
    console.log('   ✅ Health check passed\n');

    // Test 2: Admin Login
    console.log('2. Testing Admin Login...');
    const adminLoginResponse = await makeRequest('POST', '/api/auth/login', {
      email: '<EMAIL>',
      password: 'Test12345'
    });
    console.log(`   Status: ${adminLoginResponse.statusCode}`);
    if (adminLoginResponse.statusCode === 200) {
      adminToken = adminLoginResponse.body.token;
      console.log(`   Token: ${adminToken}`);
      console.log('   ✅ Admin login successful\n');
    } else {
      console.log(`   ❌ Admin login failed: ${JSON.stringify(adminLoginResponse.body)}\n`);
      return;
    }

    // Test 3: User Login
    console.log('3. Testing User Login...');
    const userLoginResponse = await makeRequest('POST', '/api/auth/login', {
      email: '<EMAIL>',
      password: 'Test1234'
    });
    console.log(`   Status: ${userLoginResponse.statusCode}`);
    if (userLoginResponse.statusCode === 200) {
      userToken = userLoginResponse.body.token;
      userId = userLoginResponse.body.user.id;
      console.log(`   Token: ${userToken}`);
      console.log('   ✅ User login successful\n');
    } else {
      console.log(`   ❌ User login failed: ${JSON.stringify(userLoginResponse.body)}\n`);
      return;
    }

    // Test 4: Get Admin Profile
    console.log('4. Testing Get Admin Profile...');
    const adminProfileResponse = await makeRequest('GET', '/api/users/profile', null, {
      'Authorization': `Bearer ${adminToken}`
    });
    console.log(`   Status: ${adminProfileResponse.statusCode}`);
    console.log(`   Profile: ${JSON.stringify(adminProfileResponse.body, null, 2)}`);
    console.log('   ✅ Admin profile retrieved\n');

    // Test 5: Get User Profile
    console.log('5. Testing Get User Profile...');
    const userProfileResponse = await makeRequest('GET', '/api/users/profile', null, {
      'Authorization': `Bearer ${userToken}`
    });
    console.log(`   Status: ${userProfileResponse.statusCode}`);
    console.log(`   Profile: ${JSON.stringify(userProfileResponse.body, null, 2)}`);
    console.log('   ✅ User profile retrieved\n');

    // Test 6: Update User Profile
    console.log('6. Testing Update User Profile...');
    const updateProfileResponse = await makeRequest('PUT', '/api/users/profile', {
      name: 'Updated Test User',
      avatar: 'https://example.com/avatar.jpg'
    }, {
      'Authorization': `Bearer ${userToken}`
    });
    console.log(`   Status: ${updateProfileResponse.statusCode}`);
    console.log(`   Updated Profile: ${JSON.stringify(updateProfileResponse.body, null, 2)}`);
    console.log('   ✅ User profile updated\n');

    // Test 7: Get All Users (Admin)
    console.log('7. Testing Get All Users (Admin)...');
    const allUsersResponse = await makeRequest('GET', '/api/users', null, {
      'Authorization': `Bearer ${adminToken}`
    });
    console.log(`   Status: ${allUsersResponse.statusCode}`);
    console.log(`   Users Count: ${allUsersResponse.body.total}`);
    console.log('   ✅ All users retrieved by admin\n');

    // Test 8: Get All Users (User - should fail)
    console.log('8. Testing Get All Users (User - should fail)...');
    const userAllUsersResponse = await makeRequest('GET', '/api/users', null, {
      'Authorization': `Bearer ${userToken}`
    });
    console.log(`   Status: ${userAllUsersResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(userAllUsersResponse.body)}`);
    if (userAllUsersResponse.statusCode === 403) {
      console.log('   ✅ User correctly denied access to admin route\n');
    } else {
      console.log('   ❌ User should not have access to admin route\n');
    }

    // Test 9: Get User by ID (Admin)
    console.log('9. Testing Get User by ID (Admin)...');
    const userByIdResponse = await makeRequest('GET', `/api/users/${userId}`, null, {
      'Authorization': `Bearer ${adminToken}`
    });
    console.log(`   Status: ${userByIdResponse.statusCode}`);
    console.log(`   User: ${JSON.stringify(userByIdResponse.body, null, 2)}`);
    console.log('   ✅ User retrieved by ID\n');

    // Test 10: Unauthorized Access
    console.log('10. Testing Unauthorized Access...');
    const unauthorizedResponse = await makeRequest('GET', '/api/users/profile');
    console.log(`   Status: ${unauthorizedResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(unauthorizedResponse.body)}`);
    if (unauthorizedResponse.statusCode === 401) {
      console.log('   ✅ Unauthorized access correctly denied\n');
    } else {
      console.log('   ❌ Should deny unauthorized access\n');
    }

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the tests
runTests();
