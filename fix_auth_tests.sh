#!/bin/bash

# <PERSON>ript to fix authentication headers in user route tests

echo "Fixing authentication headers in user route tests..."

# Fix the remaining tests in users.test.ts
sed -i 's|method: '\''GET'\'',\s*url: '\''/api/users'\'',$|method: '\''GET'\'',\n        url: '\''/api/users'\'',\n        headers: getAuthHeaders(adminUser.token),|g' tests/routes/users.test.ts

sed -i 's|method: '\''PATCH'\'',\s*url: `\/api\/users\/\${.*}\/status`,$|method: '\''PATCH'\'',\n        url: `\/api\/users\/\${user.id}\/status`,\n        headers: getAuthHeaders(adminUser.token),|g' tests/routes/users.test.ts

sed -i 's|method: '\''PATCH'\'',\s*url: `\/api\/users\/\${.*}\/role`,$|method: '\''PATCH'\'',\n        url: `\/api\/users\/\${user.id}\/role`,\n        headers: getAuthHeaders(adminUser.token),|g' tests/routes/users.test.ts

sed -i 's|method: '\''GET'\'',\s*url: '\''/api/users/profile'\'',$|method: '\''GET'\'',\n        url: '\''/api/users/profile'\'',\n        headers: getAuthHeaders(regularUser.token),|g' tests/routes/users.test.ts

sed -i 's|method: '\''PUT'\'',\s*url: '\''/api/users/profile'\'',$|method: '\''PUT'\'',\n        url: '\''/api/users/profile'\'',\n        headers: getAuthHeaders(regularUser.token),|g' tests/routes/users.test.ts

echo "Authentication headers fixed!"
