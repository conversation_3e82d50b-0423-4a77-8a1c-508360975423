import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/**',
        'tests/**',
        'dist/**',
        'coverage/**',
        '*.config.*',
        'prisma/**',
        '**/*.d.ts',
      ],
    },
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://rsdh_bot:tahshoo4sal5Fael@127.0.0.1:15432/rsdh_test',
      ADMIN_EMAIL: '<EMAIL>',
      ADMIN_PASSWORD: 'testpassword123',
      TEST_EMAIL: '<EMAIL>',
      TEST_PASSWORD: 'testpassword123',
      JWT_SECRET: 'test-jwt-secret',
      // Use real SMTP configuration from .env
      EMAIL_HOST: 'mail.diff-lab.com',
      EMAIL_PORT: '465',
      EMAIL_USER: '<EMAIL>',
      EMAIL_PASS: 'qR7X8nyLMLvdXxbeWmS1gnhmT2P96VdN',
      EMAIL_FROM: '<EMAIL>',
      APP_NAME: 'Test App',
      APP_URL: 'http://localhost:3000',
      SUPPORT_EMAIL: '<EMAIL>',
    },
    testTimeout: 10000,
    pool: 'forks', // Use forks to isolate tests better
  },
});
